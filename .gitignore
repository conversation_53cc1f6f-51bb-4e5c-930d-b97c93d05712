# dependencies (bun install)
node_modules

# output
out
dist
*.tgz

# code coverage
coverage
*.lcov

# logs
logs
_.log
report.[0-9]_.[0-9]_.[0-9]_.[0-9]_.json

# Environment variables
.env

# direnv
.direnv/

# caches
.eslintcache
.cache
*.tsbuildinfo

# IntelliJ based IDEs
.idea

# Finder (MacOS) folder config
.DS_Store

# Bun specific
bun.lockb

# Database files
*.sqlite
*.sqlite3
*.db

# Test and demo files
test-*.ts
demo-*.ts

# Editor files
.vscode/
*.swp
*.swo

# Temporary files
tmp/
temp/
