# Traefik Configuration
api:
  dashboard: true
  debug: true

entryPoints:
  web:
    address: ":80"
  websecure:
    address: ":443"

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: "traefik"

certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /letsencrypt/acme.json
      httpChallenge:
        entryPoint: web

# Global redirect to HTTPS
http:
  redirections:
    entrypoint:
      to: websecure
      scheme: https

# Access logs
accessLog:
  filePath: "/var/log/traefik/access.log"

# Traefik logs
log:
  level: INFO
  filePath: "/var/log/traefik/traefik.log"
