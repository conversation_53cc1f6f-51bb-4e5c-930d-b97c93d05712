services:
  # Traefik reverse proxy
  traefik:
    image: traefik:v3.0
    container_name: traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ../traefik/traefik.yml:/etc/traefik/traefik.yml:ro
      - traefik_letsencrypt:/letsencrypt
      - traefik_logs:/var/log/traefik
    networks:
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`traefik.${DOMAIN:-localhost}`)"
      - "traefik.http.routers.dashboard.tls=true"
      - "traefik.http.routers.dashboard.tls.certresolver=letsencrypt"

  # Application service using CI/CD built image
  app:
    image: ghcr.io/nghyane/bun-hono-starter:latest
    container_name: personal-api-prod
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-secure_password}@postgres:5432/${POSTGRES_DB:-starter_prod}
      - PORT=3000
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost:3000}
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - traefik
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api.rule=Host(`api.${DOMAIN:-localhost}`)"
      - "traefik.http.routers.api.tls=true"
      - "traefik.http.routers.api.tls.certresolver=letsencrypt"
      - "traefik.http.services.api.loadbalancer.server.port=3000"
      # Rate limiting
      - "traefik.http.middlewares.api-ratelimit.ratelimit.burst=100"
      - "traefik.http.middlewares.api-ratelimit.ratelimit.average=10"
      - "traefik.http.routers.api.middlewares=api-ratelimit"

  # PostgreSQL 17 database
  postgres:
    image: postgres:17-alpine
    container_name: personal-api-postgres-prod
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-starter_prod}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-secure_password}
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../../docs/database:/docker-entrypoint-initdb.d:ro
    networks:
      - backend
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-starter_prod}"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
    driver: local
  traefik_letsencrypt:
    driver: local
  traefik_logs:
    driver: local

networks:
  traefik:
    external: true
  backend:
    driver: bridge