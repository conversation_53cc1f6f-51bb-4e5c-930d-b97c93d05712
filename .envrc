#!/usr/bin/env bash

# Default environment variables for development
export NODE_ENV=${NODE_ENV:-development}
export PORT=${PORT:-3000}
export LOG_LEVEL=${LOG_LEVEL:-debug}

# Database configuration
export POSTGRES_DB=${POSTGRES_DB:-starter_dev}
export POSTGRES_USER=${POSTGRES_USER:-postgres}
export POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}

# DATABASE_URL must be set after the above variables
export DATABASE_URL=${DATABASE_URL:-postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@localhost:5432/${POSTGRES_DB}}

# Add your custom environment variables here
# export API_KEY=${API_KEY:-your-default-api-key}
export JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-at-least-32-characters-long}

# Add node_modules/.bin to PATH
if command -v PATH_add >/dev/null 2>&1; then
  PATH_add node_modules/.bin
else
  export PATH="$PWD/node_modules/.bin:$PATH"
fi

echo "🔧 Environment loaded: $NODE_ENV on port $PORT"
