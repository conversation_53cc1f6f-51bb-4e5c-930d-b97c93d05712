# Environment Configuration
# Copy this file to .env and customize for your environment

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
NODE_ENV=development
PORT=3000
LOG_LEVEL=debug

# Database (Development)
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/starter_dev

# CORS (Development - allow all)
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# =============================================================================
# PRODUCTION SETTINGS (uncomment and modify for production)
# =============================================================================
# NODE_ENV=production
# PORT=3000
# LOG_LEVEL=info

# Database (Production)
# POSTGRES_DB=starter_prod
# POSTGRES_USER=postgres
# POSTGRES_PASSWORD=your_secure_password_here
# DATABASE_URL=*************************************************************/starter_prod

# CORS (Production - specific domains)
# CORS_ORIGINS=https://yourdomain.com,https://api.yourdomain.com

# Domain for Traefik (Production only)
# DOMAIN=yourdomain.com

# =============================================================================
# OPTIONAL SETTINGS
# =============================================================================
# API_KEY=your-api-key
# JWT_SECRET=your-jwt-secret
# REDIS_URL=redis://localhost:6379
